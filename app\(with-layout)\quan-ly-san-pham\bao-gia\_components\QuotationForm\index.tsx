'use client';

import { IQuoteTemplateDetail } from '@/apis/quotes/quote-templates.type';
import { dealTypeOptions } from '@/app/(with-layout)/crm/co-hoi-ban-hang/constant';
import FormController from '@/components/common/FormController';
import { useMemo } from 'react';
import {
    FormProvider,
    useFieldArray,
    useForm,
    useWatch,
} from 'react-hook-form';
import { Button, Card, Col, Row } from 'reactstrap';
import ConfigurationRow from './ConfigurationRow';

interface Props {
    initValue?: IQuoteTemplateDetail;
    mode?: string;
    onSubmit?: (data: IQuoteTemplateDetail) => void;
    onCancel?: () => void;
}

const initQuoteTemplateFields = {
    fieldCode: '',
    fieldName: '',
    dataType: '',
    description: '',
    isVisible: true,
    formula: '',
    sortOrder: 0,
};

const QuotationForm = ({ initValue, mode, onSubmit, onCancel }: Props) => {
    const defaultValues = useMemo(() => {
        if (initValue) {
            return initValue;
        }

        return {
            quoteTemplateFields: [initQuoteTemplateFields],
        };
    }, [initValue]);

    const readOnly = useMemo(() => {
        if (mode === 'detail') {
            return true;
        }

        return false;
    }, [mode]);

    const methods = useForm<IQuoteTemplateDetail>({
        values: defaultValues as IQuoteTemplateDetail,
    });

    const { control, handleSubmit } = methods;

    const [quotesType] = useWatch({ control, name: ['quotesType'] });

    const {
        fields: quoteTemplateFields,
        append: appendQuoteTemplateFields,
        remove: removeQuoteTemplateFields,
    } = useFieldArray({
        control,
        name: 'quoteTemplateFields',
    });

    const handleAddQuoteTemplateField = () => {
        const newQuoteTemplateField = {
            ...initQuoteTemplateFields,
            sortOrder: quoteTemplateFields.length + 1,
        };

        appendQuoteTemplateFields(newQuoteTemplateField);
    };

    const handleFormSubmit = (data: IQuoteTemplateDetail) => {
        onSubmit(data);
    };

    return (
        <FormProvider {...methods}>
            <Card
                style={{
                    padding: '20px 40px 20px 40px',
                    minHeight: 'calc(100vh - 160px)',
                }}
                className='d-flex flex-column gap-3'
            >
                <Row className='gap-2 justify-content-around'>
                    <Col
                        md='11'
                        style={{
                            borderBottom: '1px solid rgba(216, 218, 221, 1)',
                        }}
                    >
                        <h6
                            style={{
                                color: 'rgba(10, 179, 156, 1)',
                                fontSize: '16px',
                            }}
                        >
                            {quotesType?.toString() === '1'
                                ? 'MẪU BÁO GIÁ NỘI BỘ'
                                : 'MẪU BÁO GIÁ GỬI KHÁCH HÀNG'}
                        </h6>
                    </Col>
                </Row>
                <Row className='gap-2 justify-content-around'>
                    <Col md='5'>
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên mẫu báo giá'
                            placeholder='Nhập tên mẫu báo giá...'
                            required={true}
                            readOnly={readOnly}
                        />
                    </Col>
                    <Col md='5'>
                        <FormController
                            controlType='select'
                            name='templateType'
                            label='Loại mẫu báo giá'
                            required={true}
                            placeholder='Chọn loại mẫu báo giá...'
                            data={dealTypeOptions}
                            readOnly={readOnly}
                            clearable={true}
                        />
                    </Col>
                </Row>
                <Row className='gap-3 justify-content-around'>
                    <Col md='11'>
                        <div
                            className='header-quotation-form d-flex justify-content-between p-1'
                            style={{
                                borderBottom:
                                    '1px solid rgba(216, 218, 221, 1)',
                            }}
                        >
                            <p
                                className='m-0'
                                style={{ fontWeight: 700, width: '146px' }}
                            >
                                Mã trường
                            </p>
                            <p
                                className='m-0'
                                style={{ fontWeight: 700, width: '203px' }}
                            >
                                Trường thông tin
                            </p>
                            <p
                                className='m-0'
                                style={{ fontWeight: 700, width: '220px' }}
                            >
                                Kiểu dữ liệu
                            </p>
                            <p
                                className='m-0'
                                style={{ fontWeight: 700, width: '320px' }}
                            >
                                Mô tả
                            </p>
                            <p
                                className='m-0'
                                style={{ fontWeight: 700, width: '60px' }}
                            >
                                Ẩn/ Hiện
                            </p>
                            <div
                                style={{
                                    fontWeight: 700,
                                    width: '180px',
                                    cursor: 'pointer',
                                }}
                            ></div>
                            {!readOnly && (
                                <div
                                    style={{
                                        fontWeight: 700,
                                        width: '16px',
                                        cursor: 'pointer',
                                    }}
                                ></div>
                            )}
                        </div>
                    </Col>
                    {quoteTemplateFields.map((quoteTemplateField, index) => (
                        <ConfigurationRow
                            key={index}
                            index={index}
                            onRemove={removeQuoteTemplateFields}
                            isEnabledRemove={quoteTemplateFields.length > 1}
                            readOnly={readOnly}
                        />
                    ))}
                </Row>

                {!readOnly && (
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <Button
                                color='success'
                                outline
                                size='sm'
                                onClick={handleAddQuoteTemplateField}
                            >
                                <i className='ri-add-line align-bottom me-1'></i>
                                Thêm trường
                            </Button>
                        </Col>
                    </Row>
                )}

                {!readOnly && (
                    <Row className='g-3 justify-content-around'>
                        <Col md='11' className='d-flex justify-content-between'>
                            <Button color='danger' outline onClick={onCancel}>
                                Quay lại
                            </Button>

                            <Button
                                color='success'
                                onClick={handleSubmit(handleFormSubmit)}
                            >
                                {mode === 'update' ? 'Lưu' : 'Tạo mẫu'}
                            </Button>
                        </Col>
                    </Row>
                )}
            </Card>
        </FormProvider>
    );
};

export default QuotationForm;
