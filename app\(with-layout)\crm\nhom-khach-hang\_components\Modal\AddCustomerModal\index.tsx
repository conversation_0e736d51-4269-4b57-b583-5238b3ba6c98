import { useSearchCustomer } from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { LeadStatus as LeadStatusData } from '@/constants/sharedData/sharedData';
import { useState, useEffect } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    <PERSON><PERSON>,
    Card,
    CardHeader,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Table,
    Input,
} from 'reactstrap';

// Custom CSS for checkbox styling
const checkboxStyles = `
    .custom-checkbox {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid #4dc7b6;
        border-radius: 3px;
        background-color: transparent;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
    }

    .custom-checkbox:checked {
        background-color: #4dc7b6;
        border-color: #4dc7b6;
    }

    .custom-checkbox:checked::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .custom-checkbox:hover {
        border-color: #3ba896;
    }
`;

// Inject styles into head
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = checkboxStyles;
    if (!document.head.querySelector('style[data-checkbox-custom]')) {
        styleElement.setAttribute('data-checkbox-custom', 'true');
        document.head.appendChild(styleElement);
    }
}

interface AddCustomerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: (selectedCustomers: SearchCustomerResponse[]) => void;
    excludeCustomerIds?: string[];
}

const AddCustomerModal = ({
    isOpen,
    toggle,
    onSuccess,
    excludeCustomerIds = [],
}: AddCustomerModalProps) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedData, setSelectedData] = useState<SearchCustomerResponse[]>(
        [],
    );

    // Initialize selected customers when modal opens
    useEffect(() => {
        if (isOpen) {
            if (excludeCustomerIds.length > 0) {
                setSelectedIds([...excludeCustomerIds]);
            } else {
                // Reset when modal opens without pre-selected customers
                setSelectedIds([]);
                setSelectedData([]);
            }
        }
    }, [isOpen, excludeCustomerIds]);
    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control, setValue } = methods;
    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });
    const { data, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const { items: listCustomer = [], totalItems = 0 } = data ?? {};
    const formattedCustomer = listCustomer.map(
        (Customer: SearchCustomerResponse) => ({
            ...Customer,
            createdOn: Customer.createdOn
                ? new Date(
                      Customer.createdOn.substring(0, 10),
                  ).toLocaleDateString('vi-VN')
                : '',
        }),
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Reset page when search params change
    useEffect(() => {
        setCurrentPage(1);
    }, [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
    ]);

    // Sync selectedData with selectedIds when customer data changes
    useEffect(() => {
        if (formattedCustomer.length > 0 && selectedIds.length > 0) {
            const selectedCustomers = formattedCustomer.filter((customer) =>
                selectedIds.includes(customer.id),
            );
            setSelectedData(selectedCustomers);
        } else if (selectedIds.length === 0) {
            setSelectedData([]);
        }
    }, [formattedCustomer, selectedIds]);

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentData = formattedCustomer.slice(startIndex, endIndex);

    // Handle checkbox selection
    const handleSelectCustomer = (customer: SearchCustomerResponse) => {
        const isSelected = selectedIds.includes(customer.id);
        if (isSelected) {
            setSelectedIds(selectedIds.filter((id) => id !== customer.id));
            setSelectedData(
                selectedData.filter((item) => item.id !== customer.id),
            );
        } else {
            setSelectedIds([...selectedIds, customer.id]);
            setSelectedData([...selectedData, customer]);
        }
    };

    // Helper function to get lead status label
    const getLeadStatusLabel = (leadStatus: string) => {
        // Debug: Log the values to understand the data
        if (leadStatus !== undefined && leadStatus !== null) {
            // Uncomment for debugging:
            // console.log('leadStatus:', leadStatus, 'type:', typeof leadStatus);
            // console.log('LeadStatusData values:', LeadStatusData.map(s => s.value));
        }

        // Try multiple matching strategies
        let status = LeadStatusData.find(
            (s) => String(s.value) === String(leadStatus),
        );

        // If not found, try direct number comparison
        if (!status && !isNaN(Number(leadStatus))) {
            status = LeadStatusData.find(
                (s) => Number(s.value) === Number(leadStatus),
            );
        }

        // If still not found, try case-insensitive string comparison
        if (!status && typeof leadStatus === 'string') {
            status = LeadStatusData.find(
                (s) => s.label.toLowerCase() === leadStatus.toLowerCase(),
            );
        }

        return status?.label || leadStatus || 'N/A';
    };

    // Handle select all checkbox
    const handleSelectAll = () => {
        if (
            selectedIds.length === currentData.length &&
            currentData.length > 0
        ) {
            setSelectedIds([]);
            setSelectedData([]);
        } else {
            const allIds = currentData.map((item) => item.id);
            setSelectedIds(allIds);
            setSelectedData(currentData);
        }
    };

    const handleCancel = () => {
        // Reset to pre-selected customers if any, otherwise clear all
        if (excludeCustomerIds.length > 0) {
            setSelectedIds([...excludeCustomerIds]);
            // selectedData will be updated by useEffect
        } else {
            setSelectedIds([]);
            setSelectedData([]);
        }
        toggle();
    };

    const handleSave = () => {
        if (onSuccess && selectedData.length > 0) {
            onSuccess(selectedData);
        }
        // Don't reset selected state - keep it for next time modal opens
        toggle();
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='xl'>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm khách hàng</ModalHeader>
                <ModalBody>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex flex-wrap align-items-center gap-2'>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                    />
                                </div>
                            </CardHeader>

                            {isLoading ? (
                                <div className='text-center p-4'>
                                    <div
                                        className='spinner-border'
                                        role='status'
                                    >
                                        <span className='visually-hidden'>
                                            Loading...
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped bordered hover>
                                        <thead>
                                            <tr>
                                                <th style={{ width: '50px' }}>
                                                    <Input
                                                        type='checkbox'
                                                        className='custom-checkbox'
                                                        checked={
                                                            selectedIds.length ===
                                                                currentData.length &&
                                                            currentData.length >
                                                                0
                                                        }
                                                        onChange={
                                                            handleSelectAll
                                                        }
                                                    />
                                                </th>
                                                <th>Tên khách hàng</th>
                                                <th>Loại hình</th>
                                                <th>Lĩnh vực</th>
                                                <th>Giai đoạn</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currentData.length > 0 ? (
                                                currentData.map((customer) => {
                                                    const isSelected =
                                                        selectedIds.includes(
                                                            customer.id,
                                                        );

                                                    return (
                                                        <tr key={customer.id}>
                                                            <td>
                                                                <Input
                                                                    type='checkbox'
                                                                    className='custom-checkbox'
                                                                    checked={
                                                                        isSelected
                                                                    }
                                                                    onChange={() =>
                                                                        handleSelectCustomer(
                                                                            customer,
                                                                        )
                                                                    }
                                                                />
                                                            </td>
                                                            <td>
                                                                {customer.name}
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.businessTypeName
                                                                }
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.industryName
                                                                }
                                                            </td>
                                                            <td>
                                                                <span
                                                                    className='badge me-1'
                                                                    style={{
                                                                        backgroundColor:
                                                                            '#daf4f0',
                                                                        color: '#2fbeab',
                                                                        display:
                                                                            'inline-block',
                                                                        textAlign:
                                                                            'center',
                                                                        padding:
                                                                            '4px 8px',
                                                                        fontSize:
                                                                            '12px',
                                                                        fontWeight: 500,
                                                                        borderRadius:
                                                                            '4px',
                                                                    }}
                                                                >
                                                                    {getLeadStatusLabel(
                                                                        customer.leadStatus,
                                                                    )}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    );
                                                })
                                            ) : (
                                                <tr>
                                                    <td
                                                        colSpan={6}
                                                        className='text-center'
                                                    >
                                                        Không có dữ liệu
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </Table>

                                    {totalPages > 1 && (
                                        <div className='d-flex justify-content-between align-items-center mt-3'>
                                            <div>
                                                Hiển thị {startIndex + 1} -{' '}
                                                {Math.min(endIndex, totalItems)}{' '}
                                                của {totalItems} kết quả
                                            </div>
                                            <div className='d-flex gap-2'>
                                                <Button
                                                    style={{
                                                        backgroundColor:
                                                            '#4dc7b6',
                                                        border: 'none',
                                                    }}
                                                    size='sm'
                                                    disabled={currentPage === 1}
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage - 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage - 1,
                                                        );
                                                    }}
                                                >
                                                    <i className='ri-arrow-left-s-line'></i>
                                                </Button>
                                                <span className='align-self-center'>
                                                    Trang {currentPage} /{' '}
                                                    {totalPages}
                                                </span>
                                                <Button
                                                    style={{
                                                        backgroundColor:
                                                            '#4dc7b6',
                                                        border: 'none',
                                                    }}
                                                    size='sm'
                                                    disabled={
                                                        currentPage ===
                                                        totalPages
                                                    }
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage + 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage + 1,
                                                        );
                                                    }}
                                                >
                                                    <i className='ri-arrow-right-s-line'></i>
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            )}
                        </Card>
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='danger' onClick={handleCancel}>
                        Huỷ
                    </Button>
                    <Button
                        color='success'
                        onClick={handleSave}
                        disabled={selectedData.length === 0}
                    >
                        Lưu
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddCustomerModal;
