'use client';

import { useCreateDeal } from '@/apis/opportunity/opportunity.api';
import { IOpportunity } from '@/apis/opportunity/opportunity.type';
import { KEYS_TO_OPPORTUNITY } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { RootState } from '@/store/store';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { getToday } from '@/utils/time';
import { showToastSuccess } from '@/utils/toast-message';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { createSelector } from 'reselect';
import FormOpportunity from '../_components/FormOpportunity';
import { Priority } from '../types';

const CreateOpportunity = () => {
    const selectAuthState = (state: RootState) => state.Auth;
    const selectAuth = createSelector(selectAuthState, (auth) => ({
        user: auth.user,
    }));
    const { user } = useSelector(selectAuth);

    const router = useRouter();

    const methods = useForm<IOpportunity>({
        defaultValues: {
            priority: Priority.Medium,
            customerNeed: {
                timeRequest: getToday(),
            },
            ownerId: user?.id,
            isCreateProject: false,
        },
    });

    const { setValue } = methods;

    useEffect(() => {
        if (user?.id) {
            setValue('ownerId', user.id);
        }
    }, [user?.id, setValue]);

    const { mutate: createDeal } = useCreateDeal({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới cơ hội thành công',
                message:
                    'Thông tin cơ hội đã được tạo mới thành công trong hệ thống.',
            });

            router.push(ROUTES.CRM.SALES_OPPORTUNITIES.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới cơ hội thất bại');
        },
    });

    const handleCancelCreateDeal = () => {
        router.push(ROUTES.CRM.SALES_OPPORTUNITIES.INDEX);
    };

    const handleCreateDeal = (data: IOpportunity) => {
        data.priority = Number(data.priority);

        if (data.customerNeed?.timeRequest) {
            data.customerNeed.timeRequest =
                data.customerNeed.timeRequest.replace(' ', 'T');
        }

        if (data.customerNeed?.estimatedPurchase) {
            data.customerNeed.estimatedPurchase =
                data.customerNeed.estimatedPurchase.replace(' ', 'T');
        }

        if (data.startPoC) {
            data.startPoC = data.startPoC.replace(' ', 'T');
        }

        if (data.endPoC) {
            data.endPoC = data.endPoC.replace(' ', 'T');
        }

        if (data.closeDate) {
            data.closeDate = data.closeDate.replace(' ', 'T');
        }

        if (data?.project?.watcherUserIds) {
            data.project.watcherUserIds = [
                data.project.watcherUserIds as string,
            ];
        }

        const payload = convertFormValueToPayload(data, KEYS_TO_OPPORTUNITY);

        createDeal(payload as IOpportunity);
    };

    return (
        <FormProvider {...methods}>
            <FormOpportunity
                onSubmit={handleCreateDeal}
                onCancel={handleCancelCreateDeal}
                nameSearchOwner={user?.userName ?? ''}
            />
        </FormProvider>
    );
};

export default CreateOpportunity;
